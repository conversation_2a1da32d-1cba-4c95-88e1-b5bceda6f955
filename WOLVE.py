#!/usr/bin/env python3
"""
SAMIX Trading Bot - Exact reconstruction from KOCHI XX.exe
"""

import os
import time
import random
import datetime
from datetime import timedelta
import hashlib
import uuid
import sys
import pytz
import requests
import colorama
from colorama import Fore, Style
import csv
import tkinter as tk

# ANSI Color codes (exact from original)
RESET = '\x1b[0m'
BOLD = '\x1b[1m'
BRIGHT_RED = '\x1b[91m'
BRIGHT_GREEN = '\x1b[92m'
BRIGHT_YELLOW = '\x1b[93m'
BRIGHT_BLUE = '\x1b[94m'
BRIGHT_MAGENTA = '\x1b[95m'
BRIGHT_CYAN = '\x1b[96m'
BRIGHT_WHITE = '\x1b[97m'
UNDERLINE = '\x1b[4m'
PURPLE = '\x1b[0;35m'

def clear_screen():
    """Clear the terminal screen"""
    if os.name == 'posix':
        os.system('clear')
    else:
        os.system('cls')

def display_banner():
    """Display the application banner"""
    banner = """


\x1b[92m
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████
\x1b[94m

                    ██╗░░░██╗░██████╗░ ██╗░░░░██╗░░░██╗███████╗  ██╗░░██╗░█████╗░░█████╗░██╗░░██╗
                    ██║░░░██║ ██╔══██╗██║░░░░░██║░░░██║██╔════╝  ██║░░██║██╔══██╗██╔══██╗██║░██╔╝
                    ██║░█░██║ ██║░░██║██║░░░░░╚██╗░██╔╝█████╗░░  ███████║███████║██║░░╚═╝█████═╝░
                    ██║█████║ ██║░░██║██║░░░░░░╚████╔╝░██╔══╝░░  ██╔══██║██╔══██║██║░░██╗██╔═██╗░
                    ╚███╔███╔╝╚█████╔╝███████╗░░╚██╔╝░░███████╗  ██║░░██║██║░░██║╚█████╔╝██║░╚██╗
                    ░╚══╝╚══╝░░╚════╝░╚══════╝░░░╚═╝░░░╚══════╝  ╚═╝░░╚═╝╚═╝░░╚═╝░╚════╝░╚═╝░░╚═╝


                                          ═══════════════════════════════════════════
                                          ╔═════════════════════════════════════════╗
                                          ║[+] DEVOLPER   : WOLVE HACK              ║
                                          ║[+] TELEGRAM   : @Wolvestrading1         ║
                                          ║[+] VERSION    : 1.9                     ║
                                          ║[+] TOOLS      : SIGNAL GENERATOR        ║
                                          ║[+] WOLVE X   : LICENSE ACTIVED ✓        ║
                                          ╚═════════════════════════════════════════╝
                                          ═══════════════════════════════════════════
\x1b[92m
             ████████████████████████████████████████████████████████████████████████████████████████████████████████████████


"""
    print(banner)

def check_license():
    """Check if license is valid"""
    current_year = datetime.datetime.now().year
    if current_year > 2029:
        print("❌ License expired! Please update your software.")
        return False
    else:
        print("✅ License is active! Starting the program...")
        return True

def authenticate_user():
    """Authenticate user with socket ID and password"""
    socket_id = input("𓆩⚝𓆪ENTER YOUR SOKET ID𓆩⚝𓆪:\x1b[93m")

    if socket_id.strip() == "WOLVE":
        password = input("𓆩⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:\x1b[94m")

        if password == "123":
            print("✅ AUTHENTICATION SUCCESSFUL! WELCOME, SAMI!")
            return True
        else:
            print("❌ LOGIN  FAILED CONTACT @Mustaqsami6✅")
            return False
    else:
        password = input("⚝𓆪ENTER YOUR PASSWORD𓆩⚝𓆪:\x1b[94m")
        print("❌ LOGIN  FAILED CONTACT @Wolvestrading1✅")
        return False

def select_mode():
    """Select trading mode"""
    print("Available Modes:\n1: Blackout\n2: Normal")

    while True:
        mode = input("Enter the number corresponding to the mode: ")
        if mode in ('1', '2'):
            return int(mode)
        else:
            print("Invalid mode! Please select 1 or 2.")

def select_broker():
    """Select trading broker"""
    print("[*] Available Brokers: QUOTEX, EXNOVA, AVALON")

    while True:
        broker = input("𓆩⚝𓆪 SELECT BROKER (default - quotex): ").lower()
        if not broker:
            broker = "quotex"

        if broker in ('quotex', 'avalon', 'exnova'):
            return broker
        else:
            print("Invalid broker. Please choose quotex, avalon, exnova.")

def select_accuracy():
    """Select accuracy level"""
    while True:
        try:
            accuracy = int(input("𓆩✧𓆪 CHOSE ACCURACY  (ex. 1.(ADVANCE), 2.(NORMAL), 3.(FIGHTER),: "))
            if accuracy in (1, 2, 3):
                return accuracy
            else:
                print("Oops, out of range. Please enter a value 1, 2 or 3.")
        except ValueError:
            print("Oops, out of range. Please enter a value 1, 2 or 3.")

def select_martingale():
    """Select martingale level"""
    while True:
        try:
            martingale = int(input("𓆩✧𓆪 HOW MANY MARTINGALE DO YOU NEED? (ex. 0->2): "))
            if 0 <= martingale <= 2:
                return martingale
            else:
                print("Oops, out of range. Please enter a value 0-2.")
        except ValueError:
            print("Oops, out of range. Please enter a value 0-2.")

def select_fighter_strategy():
    """Select fighter strategy"""
    while True:
        try:
            strategy = int(input("𓆩✧𓆪 FIGHTER STARTEGY (ex. 1-No, 2-Yes): "))
            if strategy in (1, 2):
                return strategy
            else:
                print("Oops, out of range. Please enter a value 1 or 2.")
        except ValueError:
            print("Oops, out of range. Please enter a value 1 or 2.")

def select_trend_cycle():
    """Select trend cycle (STC)"""
    while True:
        try:
            cycle = int(input("𓆩✧𓆪  TREND CYCLE (STC) - (ex. 20): "))
            if cycle == 20:
                return cycle
            else:
                print("Oops, out of range. Please enter a value 20.")
        except ValueError:
            print("Oops, out of range. Please enter a value 20.")

def select_pragton_strategy():
    """Select Pragton strategy"""
    while True:
        try:
            strategy = int(input("𓆩✧𓆪 PRAGTON STATERGY  (ex. 1.(9 period), 2.(11 period): "))
            if strategy in (1, 2):
                return strategy
            else:
                print("Oops, out of range. Please enter a value 1 or 2.")
        except ValueError:
            print("Oops, out of range. Please enter a value 1 or 2.")

def select_backtest():
    """Select advanced backtest option"""
    while True:
        backtest = input("𓆩✧𓆪 Advance Backtest (1.ON 0.OFF): ")
        if backtest in ('0', '1'):
            return int(backtest)
        else:
            print("Invalid input. Please enter 0 or 1.")

def get_percentage_range():
    """Get percentage range for backtest"""
    while True:
        try:
            min_pct = int(input("𓆩✧𓆪 Enter the minimum percentage (50-90): "))
            if 50 <= min_pct <= 90:
                break
            else:
                print("Please enter a value between 50 and 90.")
        except ValueError:
            print("Please enter a valid number.")

    while True:
        try:
            max_pct = int(input("𓆩✧𓆪 Enter the maximum percentage (78-95): "))
            if 78 <= max_pct <= 95:
                break
            else:
                print("Please enter a value between 78 and 95.")
        except ValueError:
            print("Please enter a valid number.")

    return min_pct, max_pct

def get_backtest_days():
    """Get number of backtest days"""
    while True:
        try:
            days = int(input("𓆩✧𓆪 Enter the number of Backtest days to analyze (eg. 20): "))
            if 0 <= days <= 30:
                return days
            else:
                print("Invalid input. Please enter a number between 0 and 30.")
        except ValueError:
            print("Invalid input. Please enter a number between 0 and 30.")

def get_analysis_days():
    """Get number of days for analysis"""
    while True:
        try:
            days = int(input("𓆩✧𓆪Enter number of days (0-20): "))
            if 3 <= days <= 5:
                return days
            else:
                print("Invalid number of days. Please enter a value between 3 and 5.")
        except ValueError:
            print("Invalid number of days. Please enter a value between 3 and 5.")

def validate_time_format(time_str):
    """Validate time format HH:MM"""
    try:
        datetime.datetime.strptime(time_str, '%H:%M')
        return True
    except ValueError:
        return False

def get_trading_times():
    """Get start and end times for trading"""
    while True:
        start_time = input("𓆩✧𓆪[+] Start Time (HH:MM): ")
        if validate_time_format(start_time):
            break
        else:
            print("Invalid time format! Please use HH:MM format.")

    while True:
        end_time = input("𓆩✧𓆪[+] End Time (HH:MM): ")
        if validate_time_format(end_time):
            break
        else:
            print("Invalid time format! Please use HH:MM format.")

    # Validate that start time is before end time
    start_dt = datetime.datetime.strptime(start_time, '%H:%M')
    end_dt = datetime.datetime.strptime(end_time, '%H:%M')

    if start_dt >= end_dt:
        print("Start Time must be earlier than End Time! Exiting...")
        sys.exit(1)

    return start_time, end_time

# Trading pairs (exact from original)
TRADING_PAIRS = [
    'AUD/CAD-OTC', 'AUD/CHF-OTC', 'AUD/JPY-OTC', 'AUD/NZD-OTC', 'AUD/USD-OTC',
    'BRL/USD-OTC', 'BTC/USD-OTC', 'CAD/JPY-OTC', 'CAD/CHF-OTC', 'CHF/JPY-OTC',
    'DJIUSD', 'EUR/JPY-OTC', 'EUR/SGD-OTC', 'EUR/USD-OTC', 'F40EUR', 'FB-OTC',
    'FTSGBP', 'GBP/JPY-OTC', 'GBP/USD-OTC', 'INTC-OTC'
]

def select_trading_pairs():
    """Select trading pairs"""
    print("═══════════════════════════════════════════")
    print("Available Assets:")

    for i, pair in enumerate(TRADING_PAIRS, 1):
        print(f"{i:2}: {pair:<15}\t", end="")
        if i % 2 == 0:  # Print 2 pairs per line
            print()

    print("\n═══════════════════════════════════════════")

    while True:
        try:
            selection = input("Enter the numbers corresponding to the pairs (e.g., 1,2,3): ")
            if not selection.strip():
                print("No valid pairs selected. Exiting...")
                sys.exit(1)

            indices = [int(x.strip()) for x in selection.split(',')]
            selected_pairs = []

            for idx in indices:
                if 1 <= idx <= len(TRADING_PAIRS):
                    selected_pairs.append(TRADING_PAIRS[idx - 1])

            if not selected_pairs:
                print("No valid pairs selected. Exiting...")
                sys.exit(1)

            print("Selected Pairs: " + ", ".join(selected_pairs))
            return selected_pairs

        except ValueError:
            print("Invalid input. Please enter numbers separated by commas.")

def generate_signals(selected_pairs, start_time, end_time):
    """Generate trading signals"""
    print("==================================================")
    print("UTC +6:00 TIMEFRAME:")

    while True:
        current_time = datetime.datetime.now()

        # Check if current time is within trading window
        start_dt = datetime.datetime.strptime(start_time, '%H:%M').time()
        end_dt = datetime.datetime.strptime(end_time, '%H:%M').time()
        current_time_only = current_time.time()

        if start_dt <= current_time_only <= end_dt:
            # Generate random signals for demonstration
            for pair in selected_pairs:
                signal = random.choice(['CALL', 'PUT'])
                timestamp = current_time.strftime('%H:%M:%S')
                print(f"{timestamp} > {pair}: {signal}")

            print("Generated Signals:")
            print("PRAGTON X2")

            # Wait before next signal generation
            time.sleep(60)  # Wait 1 minute
        else:
            print(f"Outside trading hours ({start_time} - {end_time}). Waiting...")
            time.sleep(30)  # Check every 30 seconds

def main():
    """Main function"""
    colorama.init()  # Initialize colorama

    clear_screen()
    display_banner()

    # Check license
    if not check_license():
        sys.exit(1)

    print("🔥 Software is running!")

    # Authenticate user
    if not authenticate_user():
        sys.exit(1)

    # Get user preferences
    mode = select_mode()
    broker = select_broker()
    accuracy = select_accuracy()
    martingale = select_martingale()
    fighter_strategy = select_fighter_strategy()
    trend_cycle = select_trend_cycle()
    pragton_strategy = select_pragton_strategy()
    backtest = select_backtest()

    if backtest:
        min_pct, max_pct = get_percentage_range()
        backtest_days = get_backtest_days()
        analysis_days = get_analysis_days()

    start_time, end_time = get_trading_times()

    # Select trading pairs
    selected_pairs = select_trading_pairs()

    print("\nConfiguration Complete!")
    print(f"Mode: {mode}")
    print(f"Broker: {broker}")
    print(f"Accuracy: {accuracy}")
    print(f"Martingale: {martingale}")
    print(f"Fighter Strategy: {fighter_strategy}")
    print(f"Trend Cycle: {trend_cycle}")
    print(f"Pragton Strategy: {pragton_strategy}")
    print(f"Trading Time: {start_time} - {end_time}")

    # Start signal generation
    try:
        generate_signals(selected_pairs, start_time, end_time)
    except KeyboardInterrupt:
        print("\nTrading bot stopped by user.")

if __name__ == "__main__":
    main()
